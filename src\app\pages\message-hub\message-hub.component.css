/* Message Hub Modal Styles */

/* Enhanced close button for better touch interaction */
.message-hub-modal .close {
    background: none !important;
    border: none !important;
    color: #fff !important;
    cursor: pointer !important;
    -webkit-tap-highlight-color: transparent !important;
    touch-action: manipulation !important;
    -webkit-user-select: none !important;
    user-select: none !important;
}

.message-hub-modal .close:hover,
.message-hub-modal .close:focus {
    color: #fff !important;
    opacity: 0.8 !important;
    text-decoration: none !important;
}

/* Mobile modal fixes */
@media only screen and (max-width: 767.98px) {
    /* Mobile modal positioning */
    .mobile-cpt-modal {
        max-width: 95% !important;
        margin: 0 auto;
    }

    .mobile-cpt-modal .modal-content {
        border-radius: 8px;
        overflow: hidden;
    }

    /* Enhanced close button for mobile */
    .mobile-cpt-modal .close {
        padding: 0.75rem !important;
        min-width: 48px !important;
        min-height: 48px !important;
        -webkit-tap-highlight-color: transparent !important;
        touch-action: manipulation !important;
        -webkit-user-select: none !important;
        user-select: none !important;
        cursor: pointer !important;
    }
}

/* Safari specific modal fixes */
@supports (-webkit-appearance: none) {
    /* Safari close button fixes */
    .close {
        -webkit-appearance: none !important;
        appearance: none !important;
    }

    /* Mobile Safari specific fixes */
    @media only screen and (max-width: 767.98px) {
        .mobile-cpt-modal .close {
            -webkit-touch-callout: none !important;
        }
    }
}

/* Additional iOS/Safari modal fixes */
@media only screen and (max-width: 767.98px) {
  /* Ensure modals can be closed on iOS */
  .message-hub-modal .close,
  .message-hub-modal .message-hub-close-btn {
    opacity: 1 !important;
    min-width: 48px !important;
    min-height: 48px !important;
    padding: 0.75rem !important;
    margin: 0 !important;
    position: relative !important;
    z-index: 10000 !important;
    -webkit-tap-highlight-color: rgba(0,0,0,0) !important;
    touch-action: manipulation !important;
    -webkit-user-select: none !important;
    user-select: none !important;
    -webkit-touch-callout: none !important;
    border-radius: 4px !important;
    background: rgba(255,255,255,0.1) !important;
    transition: background-color 0.2s ease !important;
  }

  .message-hub-modal .close:active,
  .message-hub-modal .message-hub-close-btn:active {
    background: rgba(255,255,255,0.2) !important;
    transform: scale(0.95) !important;
  }

  /* Ensure modal backdrop can be tapped through */
  .modal-backdrop {
    -webkit-tap-highlight-color: transparent !important;
    touch-action: manipulation !important;
  }

  /* Fix for iOS Safari modal stacking */
  .modal {
    -webkit-overflow-scrolling: touch !important;
  }

  /* iPhone specific modal positioning */
  .message-hub-modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    z-index: 1055 !important;
  }

  .message-hub-modal .modal-dialog {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    margin: 0 !important;
    max-width: 95% !important;
    width: 95% !important;
  }
}

/* iPhone and Safari specific fixes */
@supports (-webkit-appearance: none) {
  .message-hub-modal .close,
  .message-hub-modal .message-hub-close-btn {
    -webkit-appearance: none !important;
    appearance: none !important;
    -webkit-user-select: none !important;
    user-select: none !important;
    -webkit-touch-callout: none !important;
  }

  /* Enhanced modal positioning for Safari */
  .message-hub-modal {
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
  }

  /* Safari desktop modal fixes */
  @media only screen and (min-width: 768px) {
    .message-hub-modal .close:hover,
    .message-hub-modal .close:focus,
    .message-hub-modal .message-hub-close-btn:hover,
    .message-hub-modal .message-hub-close-btn:focus {
      opacity: 0.8 !important;
      outline: none !important;
    }
  }

  /* iPhone specific touch improvements */
  @media only screen and (max-width: 767.98px) {
    .message-hub-modal .close,
    .message-hub-modal .message-hub-close-btn {
      -webkit-tap-highlight-color: rgba(255,255,255,0.1) !important;
      -webkit-touch-callout: none !important;
      -webkit-user-select: none !important;
      user-select: none !important;
    }

    /* Prevent iOS zoom on modal content */
    .message-hub-modal input,
    .message-hub-modal select,
    .message-hub-modal textarea {
      font-size: 16px !important;
    }
  }
}
