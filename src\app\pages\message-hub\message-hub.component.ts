import { Component, OnInit } from '@angular/core';
import { MessageHubService } from 'src/app/services/message-hub/message-hub.service';
import { AppComponent } from 'src/app/app.component';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { ToastrService } from 'ngx-toastr';
declare let $: any;

@Component({
  selector: 'app-message-hub',
  templateUrl: './message-hub.component.html',
  styleUrls: ['./message-hub.component.css']
})
export class MessageHubComponent implements OnInit {
  public listOfMessages: Array<any> = [];
  public listOfFacilities: Array<any> = [];
  public p: number;
  public searchByName: string;
  public request: any = {};
  public device: boolean = false;
  public navigator = navigator;

  constructor(
    private readonly mgsServ: MessageHubService,
    private readonly appComp: AppComponent,
    private readonly commonServ: CommonService,
    private readonly encrDecr: EncrDecrServiceService,
    private readonly toastr: ToastrService
  ) { }

  ngOnInit() {
    this.device = this.appComp.device;
    this.appComp.loadPageName('Message Hub', 'messageHubTab');
    this.getMessages();
  }

  // Simplified device detection
  isIPhone(): boolean {
    return /iPhone|iPad|iPod/i.test(navigator.userAgent);
  }

  isSafari(): boolean {
    const userAgent = navigator.userAgent;
    return /Safari/i.test(userAgent) && !/Chrome/i.test(userAgent) && !/Chromium/i.test(userAgent);
  }

  isIPhoneOrSafari(): boolean {
    return this.isIPhone() || this.isSafari();
  }

  getMessages() {
    this.commonServ.startLoading();
    this.mgsServ.getMessages().subscribe(
      (p: any) => {
        this.listOfMessages = p;
        this.commonServ.stopLoading();
      },
      error => {
        this.commonServ.stopLoading();
      }
    );
  }

  markAllAsRead() {
    this.commonServ.startLoading();
    this.request.sGROUP_ID = this.encrDecr.set('0');
    this.request.FLAG = this.encrDecr.set('ALL');
    this.mgsServ.markAsAllRead(this.request).subscribe(
      (p: any) => {
        this.request = {};
        this.mgsServ.getMessages().subscribe(
          (p2: any) => {
            this.listOfMessages = p2;
          },
          () => { }
        );
        this.commonServ.stopLoading();
      },
      () => {
        this.request = {};
        this.commonServ.stopLoading();
      }
    );
  }

  markAsRead(groupId: string, isRead: boolean) {
    if (!isRead) {
      this.request.sGROUP_ID = this.encrDecr.set(groupId);
      this.request.FLAG = this.encrDecr.set('BADGE');
      this.mgsServ.markAsAllRead(this.request).subscribe(
        () => {
          this.request = {};
        },
        () => {
          this.request = {};
        }
      );
    }

    const modalId = '#readMsg-' + groupId;

    if (this.isIPhoneOrSafari()) {
      this.openModalSafely(modalId);
    } else {
      $(modalId).modal('show');
    }
  }

  // Custom modal opening for iPhone/Safari
  openModalSafely(modalId: string) {
    console.log('Opening modal safely for iPhone/Safari:', modalId);

    // Clean up any existing modals first
    $('.modal-backdrop').remove();
    $('body').removeClass('modal-open');
    $('.modal').hide();

    // Show the modal with iPhone-specific configuration
    $(modalId).modal({
      backdrop: 'static', // Prevent backdrop click closing on iPhone
      keyboard: false,    // Prevent ESC key closing on iPhone
      focus: true,
      show: true
    });

    // Add iPhone-specific event handlers
    $(modalId).off('click.dismiss.bs.modal').on('click.dismiss.bs.modal', (e: any) => {
      // Only close if clicking the backdrop, not the modal content
      if (e.target === e.currentTarget) {
        this.closeModalSafely(modalId);
      }
    });
  }

  archiveMessage(groupId: string) {
    this.commonServ.startLoading();
    this.request.sGROUPID = this.encrDecr.set(groupId.toString());
    this.mgsServ.archiveMessage(this.request).subscribe(
      (p: any) => {
        this.request = {};
        if (p > 0) {
          this.mgsServ.getMessages().subscribe(
            (p2: any) => {
              this.listOfMessages = p2;
            },
            () => { }
          );
          this.commonServ.stopLoading();
        }
      },
      () => {
        this.request = {};
        this.commonServ.stopLoading();
      }
    );
  }

  closeReadMgs(id: string, isRead: boolean, event?: Event) {
    const modalId = '#readMsg-' + id;

    // Prevent event bubbling/default behaviour
    if (event) {
      event.preventDefault();
      event.stopPropagation();
      event.stopImmediatePropagation();
    }

    console.log('Closing Message Hub modal:', modalId, 'iPhone/Safari:', this.isIPhoneOrSafari());

    if (this.isIPhoneOrSafari()) {
      this.closeModalSafely(modalId);
    } else {
      $(modalId).modal('hide');
    }

    if (!isRead) {
      this.mgsServ.getMessages().subscribe(
        (p: any) => {
          this.listOfMessages = p;
        },
        error => {
          console.error('Error refreshing messages:', error);
        }
      );
    }
  }

  closeModalSafely(modalId: string) {
    console.log('Closing modal safely for iPhone/Safari:', modalId);

    try {
      // 1. Immediately blur any active element to prevent focus issues
      if (document.activeElement instanceof HTMLElement) {
        document.activeElement.blur();
      }

      // 2. Force hide the modal immediately
      $(modalId).modal('hide');
      $(modalId).removeClass('show fade in');
      $(modalId).attr('aria-hidden', 'true');
      $(modalId).css({
        display: 'none !important',
        visibility: 'hidden',
        opacity: '0'
      });

      // 3. Clean up backdrop and body state
      $('.modal-backdrop').remove();
      $('body').removeClass('modal-open');
      $('body').css({
        overflow: '',
        'padding-right': '',
        position: '',
        height: '',
        width: ''
      });

      // 4. Additional cleanup for iPhone - single timeout
      setTimeout(() => {
        // Ensure all backdrops are removed
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');

        // Reset modal state completely
        $(modalId).hide();
        $(modalId).removeClass('show fade in');
        $(modalId).attr('aria-hidden', 'true');

        // Reset body and html styles
        $('html, body').css({
          overflow: 'auto',
          height: 'auto',
          position: 'static',
          width: 'auto'
        });

        console.log('Modal cleanup completed for:', modalId);
      }, 100);

    } catch (err) {
      console.error('Error in closeModalSafely:', err);
      // Simple fallback
      try {
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
        $(modalId).hide();
        $(modalId).attr('aria-hidden', 'true');
      } catch (fallbackErr) {
        console.error('Fallback modal close also failed:', fallbackErr);
      }
    }
  }

  openNotes() {
    this.commonServ.startLoading();
    this.commonServ.getFacilities().subscribe(
      (p: any) => {
        this.listOfFacilities = p;
        $('#sendMessagePop').modal('show');
        this.commonServ.stopLoading();
      },
      error => {
        console.error(error.status);
      }
    );
  }
}