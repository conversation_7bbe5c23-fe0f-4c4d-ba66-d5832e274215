import { Component, OnInit } from '@angular/core';
import { MessageHubService } from 'src/app/services/message-hub/message-hub.service';
import { AppComponent } from 'src/app/app.component';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { ToastrService } from 'ngx-toastr';
declare let $: any;

@Component({
  selector: 'app-message-hub',
  templateUrl: './message-hub.component.html',
  styleUrls: ['./message-hub.component.css']
})
export class MessageHubComponent implements OnInit {
  public listOfMessages: Array<any> = [];
  public listOfFacilities: Array<any> = [];
  public p: number;
  public searchByName: string;
  public request: any = {};
  public device: boolean = false;
  public navigator = navigator;

  constructor(
    private readonly mgsServ: MessageHubService,
    private readonly appComp: AppComponent,
    private readonly commonServ: CommonService,
    private readonly encrDecr: EncrDecrServiceService,
    private readonly toastr: ToastrService
  ) { }

  ngOnInit() {
    this.device = this.appComp.device;
    this.appComp.loadPageName('Message Hub', 'messageHubTab');
    this.getMessages();
  }

  // Simplified device detection
  isIPhone(): boolean {
    return /iPhone|iPad|iPod/i.test(navigator.userAgent);
  }

  isSafari(): boolean {
    const userAgent = navigator.userAgent;
    return /Safari/i.test(userAgent) && !/Chrome/i.test(userAgent) && !/Chromium/i.test(userAgent);
  }

  isIPhoneOrSafari(): boolean {
    return this.isIPhone() || this.isSafari();
  }

  getMessages() {
    this.commonServ.startLoading();
    this.mgsServ.getMessages().subscribe(
      (p: any) => {
        this.listOfMessages = p;
        this.commonServ.stopLoading();
      },
      error => {
        this.commonServ.stopLoading();
      }
    );
  }

  markAllAsRead() {
    this.commonServ.startLoading();
    this.request.sGROUP_ID = this.encrDecr.set('0');
    this.request.FLAG = this.encrDecr.set('ALL');
    this.mgsServ.markAsAllRead(this.request).subscribe(
      (p: any) => {
        this.request = {};
        this.mgsServ.getMessages().subscribe(
          (p2: any) => {
            this.listOfMessages = p2;
          },
          () => { }
        );
        this.commonServ.stopLoading();
      },
      () => {
        this.request = {};
        this.commonServ.stopLoading();
      }
    );
  }

  markAsRead(groupId: string, isRead: boolean) {
    if (!isRead) {
      this.request.sGROUP_ID = this.encrDecr.set(groupId);
      this.request.FLAG = this.encrDecr.set('BADGE');
      this.mgsServ.markAsAllRead(this.request).subscribe(
        () => {
          this.request = {};
        },
        () => {
          this.request = {};
        }
      );
    }

    const modalId = '#readMsg-' + groupId;

    if (this.isIPhoneOrSafari()) {
      this.openModalSafely(modalId);
    } else {
      $(modalId).modal('show');
    }
  }

  // Custom modal opening for iPhone/Safari
  openModalSafely(modalId: string) {
    $('.modal-backdrop').remove();
    $('body').removeClass('modal-open');
    $('.modal').hide();
    $(modalId).modal('show');
  }

  archiveMessage(groupId: string) {
    this.commonServ.startLoading();
    this.request.sGROUPID = this.encrDecr.set(groupId.toString());
    this.mgsServ.archiveMessage(this.request).subscribe(
      (p: any) => {
        this.request = {};
        if (p > 0) {
          this.mgsServ.getMessages().subscribe(
            (p2: any) => {
              this.listOfMessages = p2;
            },
            () => { }
          );
          this.commonServ.stopLoading();
        }
      },
      () => {
        this.request = {};
        this.commonServ.stopLoading();
      }
    );
  }

  closeReadMgs(id: string, isRead: boolean, event?: Event) {
    const modalId = '#readMsg-' + id;

    // Prevent event bubbling/default behaviour
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    if (this.isIPhoneOrSafari()) {
      this.closeModalSafely(modalId);
    } else {
      $(modalId).modal('hide');
    }

    if (!isRead) {
      this.mgsServ.getMessages().subscribe(
        (p: any) => {
          this.listOfMessages = p;
        },
        error => {
          console.error('Error refreshing messages:', error);
        }
      );
    }
  }

  closeModalSafely(modalId: string) {
    console.log('Closing modal safely for iPhone/Safari:', modalId);

    try {
      // 1. Prevent any further interactions
      $(modalId).css('pointer-events', 'none');

      // 2. Blur any active element inside the modal
      if (
        document.activeElement instanceof HTMLElement &&
        $(modalId).find(document.activeElement).length > 0
      ) {
        document.activeElement.blur();
      }

      // 3. Move focus to body
      document.body.focus();

      // 4. Remove modal from accessibility tree
      $(modalId)
        .find('button, a, input, select, textarea, [tabindex]')
        .attr('tabindex', '-1')
        .prop('disabled', true);

      // 5. Force hide the modal
      $(modalId).modal('hide');
      $(modalId).removeClass('show fade in');
      $(modalId).attr('aria-hidden', 'true');
      $(modalId).css({
        display: 'none',
        'z-index': '-1',
        opacity: '0',
        visibility: 'hidden'
      });

      // 6. Remove backdrop and reset body
      $('.modal-backdrop').remove();
      $('body').removeClass('modal-open');
      $('body').css({
        overflow: '',
        'padding-right': '',
        position: '',
        height: '',
        width: ''
      });

      // 7. First cleanup pass
      setTimeout(() => {
        $('.modal-backdrop').remove();
        $(modalId).hide();
        $(modalId).css('pointer-events', 'auto');
        $('body').removeClass('modal-open');
        $('html, body').css({
          overflow: 'auto',
          height: 'auto',
          position: 'static',
          width: 'auto'
        });
        console.log('Modal cleanup phase 1 completed for:', modalId);
      }, 50);

      // 8. Second cleanup pass
      setTimeout(() => {
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
        $(modalId).removeClass('show fade in');
        $(modalId).attr('aria-hidden', 'true');

        $('.modal').each((_index: number, element: any) => {
          const $element = $(element);
          if ($element.hasClass('show')) {
            $element.removeClass('show');
            $element.attr('aria-hidden', 'true');
            $element.css('display', 'none');
          }
        });

        console.log('Modal cleanup phase 2 completed for:', modalId);
      }, 150);

      // 9. Final cleanup pass
      setTimeout(() => {
        $(document).find('.modal-backdrop').remove();
        $('body').removeClass('modal-open');

        if ($('.modal-backdrop').length > 0) {
          $('.modal-backdrop').remove();
        }

        console.log('Final modal cleanup completed for:', modalId);
      }, 300);
    } catch (err) {
      console.error('Error in closeModalSafely:', err);
      // Fallback if needed
      try {
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
        $(modalId).hide();
        $(modalId).removeClass('show fade in');
        $(modalId).attr('aria-hidden', 'true');
        $(modalId).css({
          display: 'none',
          'z-index': '-1',
          opacity: '0',
          visibility: 'hidden'
        });
      } catch (fallbackErr) {
        console.error('Fallback modal close also failed:', fallbackErr);
      }
    }
  }

  openNotes() {
    this.commonServ.startLoading();
    this.commonServ.getFacilities().subscribe(
      (p: any) => {
        this.listOfFacilities = p;
        $('#sendMessagePop').modal('show');
        this.commonServ.stopLoading();
      },
      error => {
        console.error(error.status);
      }
    );
  }
}